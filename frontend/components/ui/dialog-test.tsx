"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel } from "@/components/ui/alert-dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { MoreHorizon<PERSON>, Settings, Trash2, Edit, Info } from "lucide-react"

/**
 * Test component to verify all modal, dialog, and floating UI improvements
 * This component tests:
 * 1. Dialog positioning (mobile bottom sheet, desktop centered)
 * 2. Alert dialog positioning
 * 3. Dropdown menu positioning and overflow prevention
 * 4. Popover positioning
 * 5. Large dialog size for complex forms
 */
export function DialogTest() {
  const [showDialog, setShowDialog] = useState(false)
  const [showLargeDialog, setShowLargeDialog] = useState(false)
  const [showAlertDialog, setShowAlertDialog] = useState(false)

  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Dialog & Modal Test Suite</h1>
        <p className="text-gray-600">
          Test all modal, dialog, and floating UI components for device-agnostic positioning
        </p>
      </div>

      {/* Test Buttons Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Standard Dialog Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Standard Dialog</h3>
          <p className="text-sm text-gray-600">
            Mobile: Bottom sheet with margins<br/>
            Desktop: Centered modal (max-w-lg)
          </p>
          <Button onClick={() => setShowDialog(true)} className="w-full">
            Open Standard Dialog
          </Button>
        </div>

        {/* Large Dialog Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Large Dialog</h3>
          <p className="text-sm text-gray-600">
            Mobile: Bottom sheet with margins<br/>
            Desktop: Centered modal (max-w-2xl)
          </p>
          <Button onClick={() => setShowLargeDialog(true)} className="w-full">
            Open Large Dialog
          </Button>
        </div>

        {/* Alert Dialog Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Alert Dialog</h3>
          <p className="text-sm text-gray-600">
            Mobile: Bottom sheet<br/>
            Desktop: Centered confirmation
          </p>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" className="w-full">
                Open Alert Dialog
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete your account
                  and remove your data from our servers.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction>Continue</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {/* Dropdown Menu Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Dropdown Menu</h3>
          <p className="text-sm text-gray-600">
            Enhanced positioning with overflow prevention
          </p>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full">
                <MoreHorizontal className="h-4 w-4 mr-2" />
                Open Menu
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Popover Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Popover</h3>
          <p className="text-sm text-gray-600">
            Enhanced positioning with overflow prevention
          </p>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full">
                <Info className="h-4 w-4 mr-2" />
                Open Popover
              </Button>
            </PopoverTrigger>
            <PopoverContent>
              <div className="space-y-3">
                <h4 className="font-semibold">Information</h4>
                <p className="text-sm text-gray-600">
                  This is a popover with enhanced positioning that prevents overflow
                  on mobile devices and provides smooth animations.
                </p>
                <div className="flex gap-2">
                  <Badge variant="secondary">Enhanced</Badge>
                  <Badge variant="outline">Responsive</Badge>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Edge Case Test */}
        <div className="p-6 border rounded-xl space-y-4">
          <h3 className="font-semibold">Edge Cases</h3>
          <p className="text-sm text-gray-600">
            Test positioning near screen edges
          </p>
          <div className="space-y-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" className="w-full">
                  Bottom Right Menu
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Item 1</DropdownMenuItem>
                <DropdownMenuItem>Item 2</DropdownMenuItem>
                <DropdownMenuItem>Item 3</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Standard Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Standard Dialog</DialogTitle>
            <DialogDescription>
              This is a standard dialog that should appear as a bottom sheet on mobile
              and a centered modal on desktop.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input id="name" placeholder="Enter your name" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="Enter your email" />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowDialog(false)}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Large Dialog */}
      <Dialog open={showLargeDialog} onOpenChange={setShowLargeDialog}>
        <DialogContent size="large">
          <DialogHeader>
            <DialogTitle>Large Dialog (Complex Form)</DialogTitle>
            <DialogDescription>
              This is a large dialog suitable for complex forms like the scoring rule editor.
              It uses max-w-2xl on desktop for more space.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input id="title" placeholder="Enter title" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Input id="category" placeholder="Enter category" />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                placeholder="Enter description" 
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="weight">Weight</Label>
                <Input id="weight" type="number" placeholder="1.0" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="threshold">Threshold</Label>
                <Input id="threshold" type="number" placeholder="0.5" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Input id="priority" placeholder="High" />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLargeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowLargeDialog(false)}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
